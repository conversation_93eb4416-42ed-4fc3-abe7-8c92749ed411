"""
数据获取Handler - 负责从数据源获取待处理的作品数据

支持两种数据获取方式：
1. 从服务器查询 (需要api_token) - 参考 old/xhs_detail_updater.py 的 __query_tasks_rows 方法
2. 从多维表获取 (使用jss-api-extend) - 参考 jss_api_extend/bitable/base_dingtalk_bitable_notifier.py 的 query_records 方法
"""

import logging
import re
from typing import List, Dict, Any, Optional


from models.work_models import WorkDetailTaskModel
from .base_handler import BaseHandler
from ..context import ClientConfig
from ..exceptions import DataFetchException
from ..pipeline_context import PipelineContext


class DataHandler(BaseHandler):
    """
    数据获取处理器基类

    职责：
    - 从配置的数据源获取待处理的作品列表
    - 统一处理为标准的数据格式
    - 应用过滤规则
    - 设置上下文中的待处理数据
    """

    def __init__(self, client_config: ClientConfig, logger):
        super().__init__(client_config, logger)

        self.logger = logger.getChild(self.__class__.__name__)
        self.dingtalk_notifier_client = BaseDingTalkBitableNotifier(
            client_config.bitable.get("app_key"),
            client_config.bitable.get("app_secret"),
            client_config.bitable.get("refresh_token")
        )

    def handle(self, context: PipelineContext) -> PipelineContext:
        """
        处理数据获取逻辑

        """
        try:
            self.logger.info(f"Starting data fetch for client: {context.client_name}")

            # 获取数据
            tasks = self._fetch_data(context)

            # 应用过滤规则
            filtered_tasks = self._apply_filters(tasks, context)

            # 设置到上下文
            context.task_list = filtered_tasks

            self.logger.info(f"Fetched {len(filtered_tasks)} tasks for processing")

            # 传递给下一个处理器
            return super().handle(context)

        except Exception as e:
            self.logger.error(f"Data fetch failed: {e}")
            raise DataFetchException(f"Failed to fetch data: {str(e)}")

    def _fetch_data(self, context: PipelineContext) -> List[WorkDetailTaskModel]:
        """
        从数据源获取数据 - 根据配置的source_type自动选择数据源

        Args:
            context: Pipeline上下文

        Returns:
            待处理的作品任务列表
        """
        # 从配置中获取数据源类型
        source_type = self.client_config.data_fetcher.get('source_type', 'dingtalk_bitable')

        self.logger.info(f"Using data source type: {source_type}")

        if source_type == 'dingtalk_bitable':
            return self._fetch_from_bitable(context)
        elif source_type == 'service_client':
            return self._fetch_from_service(context)
        else:
            raise DataFetchException(f"Unsupported source type: {source_type}")

    def _fetch_from_bitable(self, context: PipelineContext) -> List[WorkDetailTaskModel]:
        """
        从钉钉多维表获取数据 - 参考 old/xhs_detail_updater.py 的 __query_format_task_list 方法

        Args:
            context: Pipeline上下文

        Returns:
            待处理的作品任务列表
        """
        try:
            # 获取表格信息 - 从服务端查询
            table_info = self._query_update_table_one()
            if not table_info:
                self.logger.info("No table info found from service")
                return []

            dentry_uuid = table_info.get("dentryUuid")
            id_or_name = table_info.get("idOrName")
            max_update_times = table_info.get("updateTimes")

            if not all([dentry_uuid, id_or_name, max_update_times]):
                self.logger.error(f"Incomplete table info from service: {table_info}")
                return []

            self.logger.info(
                f"Fetching from bitable: dentry_uuid={dentry_uuid}, id_or_name={id_or_name}, max_update_times={max_update_times}"
            )

            # 使用钉钉通知器获取多维表数据
            if not hasattr(self, 'dingtalk_notifier_client'):
                self.dingtalk_notifier_client = BaseDingTalkBitableNotifier(
                    self.client_config.bitable.get("app_key"),
                    self.client_config.bitable.get("app_secret"),
                    self.client_config.bitable.get("agent_id"),
                    self.client_config.bitable.get("operator_id")
                )

            # 获取多维表数据
            record_list = self.dingtalk_notifier_client.list_bitable_data_by_api_filter(
                dentry_uuid, id_or_name, max_update_times
            )

            if not record_list:
                self.logger.info(f"No records found in bitable: {dentry_uuid}")
                return []

            # 转换为任务列表
            task_list = []
            for record in record_list:
                task = self._convert_bitable_record_to_task(record, context.batch_id, dentry_uuid, id_or_name)
                if task:
                    task_list.append(task)
                else:
                    # 记录转换失败的URL
                    context.error_format_url_list.append(record.work_url)

            self.logger.info(f"Successfully converted {len(task_list)} tasks from bitable")
            return task_list

        except Exception as e:
            self.logger.error(f"Failed to fetch data from bitable: {e}")
            raise DataFetchException(f"Bitable data fetch failed: {str(e)}")

    def _fetch_from_service(self, context: PipelineContext) -> List[WorkDetailTaskModel]:
        """
        从服务端API获取数据

        Args:
            context: Pipeline上下文

        Returns:
            待处理的作品任务列表
        """
        # TODO: 实现服务端API数据获取逻辑
        self.logger.warning("Service client data fetching not implemented yet")
        return []

    def _query_update_table_one(self) -> Optional[Dict[str, Any]]:
        """
        从服务端查询表格信息

        Returns:
            表格信息字典，包含 dentryUuid, idOrName, updateTimes
        """
        try:

            bitable_config = self.client_config.bitable
            if not bitable_config:
                return None

            return {
                "dentryUuid": bitable_config.get("dentryUuid"),
                "idOrName": bitable_config.get("idOrName"),
                "updateTimes": self.client_config.data_fetcher.get("filter_rules", {}).get("update_times_limit", 5)
            }
        except Exception as e:
            self.logger.error(f"Failed to query table info: {e}")
            return None

    def _convert_bitable_record_to_task(self, record, batch_id: str, dentry_uuid: str, id_or_name: str) -> Optional[WorkDetailTaskModel]:
        """
        将多维表记录转换为任务模型

        Args:
            record: 多维表记录
            batch_id: 批次ID
            dentry_uuid: 表格UUID
            id_or_name: 表格ID或名称

        Returns:
            转换后的任务模型
        """
        try:
            # 提取平台和作品ID
            result = self._extract_platform_and_work_id(record.work_url)
            if result is None:
                self.logger.warning(f"Failed to extract platform/work_id from URL: {record.work_url}")
                return None

            platform, work_id = result
            if not platform or not work_id:
                self.logger.warning(f"Empty platform or work_id. Platform: {platform}, WorkId: {work_id}")
                return None

            # 创建任务模型
            task = WorkDetailTaskModel(
                id=None,
                work_url=record.work_url,
                platform=platform,
                work_id=work_id,
                row_id=record.row_id,
                submit_user=record.user_union_id,
                dentry_uuid=dentry_uuid,
                id_or_name=id_or_name,
                update_count=record.update_count,
                extends=record.extends
            )

            return task

        except Exception as e:
            self.logger.error(f"Failed to convert bitable record to task: {e}")
            return None

    def _apply_filters(self, tasks: List[WorkDetailTaskModel], context: PipelineContext) -> List[WorkDetailTaskModel]:
        """
        应用过滤规则
        
        Args:
            tasks: 原始任务列表
            context: 工作上下文
            
        Returns:
            过滤后的任务列表
        """
        if not tasks:
            return tasks

        filtered_tasks = tasks

        # 应用更新次数限制
        update_limit = self._get_update_limit()
        if update_limit > 0:
            filtered_tasks = [
                task for task in filtered_tasks
                if task.update_count < update_limit
            ]
            self.logger.info(f"Applied update limit filter: {len(filtered_tasks)} tasks remaining")

        # 应用平台过滤
        allowed_platforms = self._get_allowed_platforms()
        if allowed_platforms:
            filtered_tasks = [
                task for task in filtered_tasks
                if task.platform in allowed_platforms
            ]
            self.logger.info(f"Applied platform filter: {len(filtered_tasks)} tasks remaining")

        return filtered_tasks

    def _get_update_limit(self) -> int:
        """获取更新次数限制"""
        data_fetcher_config = self.client_config.data_fetcher_config or {}
        filter_rules = data_fetcher_config.get('filter_rules', {})
        return filter_rules.get('update_times_limit', 0)

    def _get_allowed_platforms(self) -> List[str]:
        """获取允许的平台列表"""
        work_updater_config = self.client_config.work_updater_config or {}
        return work_updater_config.get('platforms', [])

    def _extract_platform_and_work_id(self, work_url: str) -> tuple[str, str]:
        """
        从作品URL提取平台和作品ID

        Args:
            work_url: 作品URL

        Returns:
            (platform, work_id) 元组
        """
        if not work_url:
            return None, None

        # 小红书
        if 'xiaohongshu.com' in work_url or 'xhslink.com' in work_url:
            match = re.search(r'/item/([a-f0-9]+)', work_url)
            if match:
                return 'xhs', match.group(1)

        # 抖音
        elif 'douyin.com' in work_url:
            match = re.search(r'/video/(\d+)', work_url)
            if match:
                return 'dy', match.group(1)

        # 快手
        elif 'kuaishou.com' in work_url:
            match = re.search(r'/photo/(\d+)', work_url)
            if match:
                return 'ks', match.group(1)

        # 微信视频号
        elif 'channels.weixin.qq.com' in work_url:
            match = re.search(r'/video/([a-zA-Z0-9_-]+)', work_url)
            if match:
                return 'wxvideo', match.group(1)

        # 默认返回URL的hash作为work_id
        return 'unknown', str(hash(work_url))

