#!/usr/bin/env python3
"""
测试 DataHandler 的修改
"""

import logging
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.handlers.data_handler import DataHandler
from core.pipeline_context import PipelineContext
from core.context import ClientConfig

def test_data_handler():
    """测试 DataHandler 的基本功能"""
    
    # 创建日志器
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # 创建模拟的客户端配置
    client_config = ClientConfig(
        name="test_client",
        data_fetcher={
            "source_type": "dingtalk_bitable",
            "filter_rules": {
                "update_times_limit": 5,
                "platform": "all"
            }
        },
        bitable={
            "app_key": "test_app_key",
            "app_secret": "test_app_secret", 
            "agent_id": "test_agent_id",
            "operator_id": "test_operator_id",
            "dentryUuid": "test_dentry_uuid",
            "idOrName": "test_id_or_name"
        }
    )
    
    # 创建 DataHandler 实例
    data_handler = DataHandler(client_config, logger)
    
    # 创建 Pipeline 上下文
    context = PipelineContext(
        client_name="test_client",
        batch_id="test_batch_001",
        client_config=client_config
    )
    
    print("✅ DataHandler 实例创建成功")
    print(f"   配置的数据源类型: {client_config.data_fetcher.get('source_type')}")
    print(f"   上下文客户端名称: {context.client_name}")
    print(f"   批次ID: {context.batch_id}")
    
    # 测试数据源类型判断
    source_type = client_config.data_fetcher.get('source_type', 'dingtalk_bitable')
    print(f"   检测到的数据源类型: {source_type}")
    
    if source_type == 'dingtalk_bitable':
        print("✅ 将使用钉钉多维表数据源")
    elif source_type == 'service_client':
        print("✅ 将使用服务端API数据源")
    else:
        print(f"❌ 不支持的数据源类型: {source_type}")
    
    print("\n🎉 DataHandler 修改验证完成！")

if __name__ == "__main__":
    test_data_handler()
