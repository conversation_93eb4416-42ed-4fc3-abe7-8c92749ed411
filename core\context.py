"""
Context classes for the work monitor system.

This module defines the context objects that carry data through
the chain of responsibility handlers.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging


@dataclass
class ClientConfig:
    """Configuration for a specific client."""

    name: str

    # 调度配置
    schedule: Dict[str, Any] = field(default_factory=dict)

    # 数据获取配置
    data_fetcher: Dict[str, Any] = field(default_factory=dict)

    # 作品更新配置
    work_updater: Dict[str, Any] = field(default_factory=dict)

    # 通知配置
    notification: Dict[str, Any] = field(default_factory=dict)

    # 用户信息配置
    user_info: Dict[str, Any] = field(default_factory=dict)

    # bitable配置
    bitable: Dict[str, Any] = field(default_factory=dict)

    # 数据同步配置
    data_sync: Dict[str, Any] = field(default_factory=dict)
    


    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ClientConfig':
        """Create ClientConfig from dictionary."""
        return cls(
            name=data.get('name', ''),
            schedule=data.get('schedule', {}),
            data_fetcher=data.get('data_fetcher', {}),
            work_updater=data.get('work_updater', {}),
            notification=data.get('notification', {}),
            user_info=data.get('user_info', {}),
            bitable=data.get('bitable', {}),
            data_sync=data.get('data_sync', {})
        )

