"""
Base handler class for the chain of responsibility pattern.
"""

from abc import ABC, abstractmethod
from typing import Optional, Any
import logging
import time

from ..context import ClientConfig
from ..pipeline_context import PipelineContext
from ..exceptions import HandlerException


class BaseHandler(ABC):
    """
    Base class for all handlers in the chain of responsibility.

    Each handler should implement the handle method to process the context
    and optionally pass it to the next handler in the chain.
    """

    def __init__(self, client_config: ClientConfig, logger: logging.Logger):
        """
        Initialize the handler.

        Args:
            client_config: Configuration for the specific client
            logger: Logger instance for this handler
        """
        self.client_config = client_config
        self.logger = logger
        self.next_handler: Optional["BaseHandler"] = None
        self.handler_name = self.__class__.__name__

    def set_next(self, handler: "BaseHandler") -> "BaseHandler":
        """
        Set the next handler in the chain.

        Args:
            handler: The next handler to be called

        Returns:
            The handler that was set as next (for method chaining)
        """
        self.next_handler = handler
        return handler

    @abstractmethod
    def handle(self, context: PipelineContext) -> PipelineContext:
        """
        Handle the context and process the work.

        Args:
            context: The pipeline context containing all necessary data

        Returns:
            The updated pipeline context

        Raises:
            HandlerException: If an error occurs during processing
        """
        pass

    def handle_next(self, context: PipelineContext) -> PipelineContext:
        """
        Pass the context to the next handler in the chain.

        Args:
            context: The pipeline context to pass to the next handler

        Returns:
            The updated pipeline context from the next handler, or the original context if no next handler
        """
        if self.next_handler:
            return self.next_handler.handle(context)
        return context

    def execute_with_logging(self, context: PipelineContext) -> PipelineContext:
        """
        Execute the handler with comprehensive logging and error handling.

        Args:
            context: The pipeline context to process

        Returns:
            The updated pipeline context
        """
        start_time = time.time()

        try:
            self.logger.info(
                f"[{self.handler_name}] Starting processing for client: {context.client_name}"
            )

            # Execute the actual handler logic
            result_context = self.handle(context)

            execution_time = time.time() - start_time

            self.logger.info(
                f"[{self.handler_name}] Processing completed successfully in {execution_time:.2f}s"
            )

            return result_context

        except HandlerException as e:
            execution_time = time.time() - start_time
            self.logger.error(
                f"[{self.handler_name}] Handler exception after {execution_time:.2f}s: {e.message}"
            )
            if e.details:
                self.logger.error(f"[{self.handler_name}] Error details: {e.details}")

            context.add_error(f"{self.handler_name}: {e.message}")
            return False

        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(
                f"[{self.handler_name}] Unexpected exception after {execution_time:.2f}s: {str(e)}"
            )

            context.add_error(f"{self.handler_name}: Unexpected error - {str(e)}")
            return False

    def validate_context(self, context: WorkContext) -> bool:
        """
        Validate that the context has the required data for this handler.

        Args:
            context: The work context to validate

        Returns:
            True if context is valid, False otherwise
        """
        if not context.client_name:
            self.logger.error(f"[{self.handler_name}] Missing client_name in context")
            return False

        if not context.batch_id:
            self.logger.error(f"[{self.handler_name}] Missing batch_id in context")
            return False

        return True

    def should_continue_chain(self, context: WorkContext, current_result: bool) -> bool:
        """
        Determine if the chain should continue to the next handler.

        Args:
            context: The current work context
            current_result: The result of the current handler

        Returns:
            True if the chain should continue, False otherwise
        """
        # By default, continue if current handler succeeded
        # Subclasses can override this for custom logic
        return current_result

    def get_handler_config(self, config_key: str, default: Any = None) -> Any:
        """
        Get configuration value for this handler.

        Args:
            config_key: The configuration key to retrieve
            default: Default value if key is not found

        Returns:
            The configuration value or default
        """
        # This method can be overridden by subclasses to access specific config sections
        return getattr(self.client_config, config_key, default)
